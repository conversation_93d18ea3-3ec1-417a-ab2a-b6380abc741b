/**
 * Voice recording button with waveform visualization.
 * Premium feature for speech-to-text input.
 */

import React, { useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../hooks/useTheme';
import { useVoice } from '../../hooks/useVoice';

interface VoiceButtonProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const { width: screenWidth } = Dimensions.get('window');

export const VoiceButton: React.FC<VoiceButtonProps> = ({
  onTranscript,
  disabled = false,
  size = 'medium',
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const {
    recordingState,
    isVoiceSupported,
    isPremium,
    startRecording,
    stopRecording,
    checkPermissions,
  } = useVoice();

  const pulseAnim = new Animated.Value(1);

  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  useEffect(() => {
    if (recordingState.isRecording) {
      // Start pulsing animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Stop animation
      pulseAnim.setValue(1);
    }
  }, [recordingState.isRecording, pulseAnim]);

  useEffect(() => {
    if (recordingState.finalText) {
      onTranscript(recordingState.finalText);
    }
  }, [recordingState.finalText, onTranscript]);

  const handlePress = async () => {
    if (!isVoiceSupported) {
      Alert.alert(
        t('voice.notSupported'),
        t('voice.notSupportedMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (!isPremium) {
      Alert.alert(
        t('voice.premiumRequired'),
        t('voice.premiumRequiredMessage'),
        [
          { text: t('cancel'), style: 'cancel' },
          { text: t('voice.upgradeToPremium'), onPress: () => {
            // TODO: Navigate to premium upgrade screen
            console.log('Navigate to premium upgrade');
          }},
        ]
      );
      return;
    }

    try {
      if (recordingState.isRecording) {
        await stopRecording();
      } else {
        await startRecording();
      }
    } catch (error) {
      console.error('Voice recording error:', error);
      Alert.alert(
        t('error'),
        error instanceof Error ? error.message : t('voice.recordingError'),
        [{ text: t('ok') }]
      );
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small': return 40;
      case 'large': return 64;
      default: return 52;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 24;
      default: return 20;
    }
  };

  const buttonSize = getButtonSize();
  const iconSize = getIconSize();

  const isActive = recordingState.isRecording;
  const isProcessing = recordingState.isProcessing;
  const hasError = !!recordingState.error;

  const buttonColor = hasError 
    ? theme.colors.error 
    : isActive 
      ? theme.colors.secondary 
      : theme.colors.primary;

  const renderWaveform = () => {
    if (!isActive || recordingState.waveformData.length === 0) {
      return null;
    }

    const waveformWidth = screenWidth - 120; // Leave space for button and margins
    const barWidth = 2;
    const barSpacing = 1;
    const maxBars = Math.floor(waveformWidth / (barWidth + barSpacing));
    const data = recordingState.waveformData.slice(0, maxBars);

    return (
      <View style={[styles.waveformContainer, { width: waveformWidth }]}>
        {data.map((value, index) => (
          <View
            key={index}
            style={[
              styles.waveformBar,
              {
                height: Math.max(2, value * 30),
                backgroundColor: theme.colors.secondary,
                width: barWidth,
                marginRight: barSpacing,
              },
            ]}
          />
        ))}
      </View>
    );
  };

  const renderTranscriptText = () => {
    const text = recordingState.partialText || recordingState.finalText;
    if (!text) return null;

    return (
      <View style={[styles.transcriptContainer, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.transcriptText, { color: theme.colors.text }]}>
          {text}
          {recordingState.partialText && (
            <Text style={[styles.partialIndicator, { color: theme.colors.textSecondary }]}>
              {' '}●
            </Text>
          )}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderTranscriptText()}
      
      <View style={styles.controlsContainer}>
        {renderWaveform()}
        
        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
          <TouchableOpacity
            style={[
              styles.voiceButton,
              {
                width: buttonSize,
                height: buttonSize,
                backgroundColor: buttonColor,
                opacity: disabled ? 0.5 : 1,
              },
            ]}
            onPress={handlePress}
            disabled={disabled || isProcessing}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              {isActive ? (
                <View style={[styles.stopIcon, { 
                  width: iconSize * 0.6, 
                  height: iconSize * 0.6,
                  backgroundColor: theme.colors.surface 
                }]} />
              ) : (
                <View style={[styles.micIcon, { 
                  width: iconSize, 
                  height: iconSize,
                  borderColor: theme.colors.surface 
                }]} />
              )}
            </View>
            
            {isProcessing && (
              <View style={[styles.processingIndicator, {
                borderColor: theme.colors.surface,
                borderTopColor: 'transparent',
              }]} />
            )}
          </TouchableOpacity>
        </Animated.View>
      </View>

      {recordingState.error && (
        <View style={[styles.errorContainer, { backgroundColor: theme.colors.errorSurface }]}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {recordingState.error}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 8,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  voiceButton: {
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  micIcon: {
    borderWidth: 2,
    borderRadius: 4,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  stopIcon: {
    borderRadius: 2,
  },
  processingIndicator: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 10,
    borderStyle: 'solid',
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    paddingHorizontal: 8,
  },
  waveformBar: {
    borderRadius: 1,
  },
  transcriptContainer: {
    maxWidth: screenWidth - 40,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  transcriptText: {
    fontSize: 14,
    textAlign: 'center',
  },
  partialIndicator: {
    fontSize: 12,
  },
  errorContainer: {
    maxWidth: screenWidth - 40,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
  },
});
