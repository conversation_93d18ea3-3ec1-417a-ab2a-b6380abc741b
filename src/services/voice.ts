/**
 * Voice service for speech-to-text and text-to-speech functionality.
 * Premium feature using Whisper.cpp and XTTS.
 */

import { Platform } from 'react-native';
import { auth } from '../config/firebase';
import { apiService } from './api';

export interface STTResponse {
  type: 'partial' | 'final' | 'error';
  text: string;
  confidence: number;
}

export interface TTSRequest {
  text: string;
  voice?: string;
  language?: string;
}

export interface VoiceRecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  partialText: string;
  finalText: string;
  error: string | null;
  waveformData: number[];
}

class VoiceService {
  private sttWebSocket: WebSocket | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private isRecording = false;
  private recordingChunks: Blob[] = [];

  private get baseUrl(): string {
    return process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  }

  private get wsUrl(): string {
    const baseUrl = this.baseUrl;
    const wsBaseUrl = baseUrl.replace(/^https?:\/\//, '').replace(/\/api$/, '');
    const protocol = baseUrl.startsWith('https') ? 'wss' : 'ws';
    return `${protocol}://${wsBaseUrl}`;
  }

  /**
   * Check if voice features are supported on this platform.
   */
  isVoiceSupported(): boolean {
    if (Platform.OS === 'web') {
      return !!(
        navigator.mediaDevices &&
        navigator.mediaDevices.getUserMedia &&
        window.MediaRecorder &&
        window.AudioContext
      );
    }
    // React Native support would require expo-av or similar
    return false;
  }

  /**
   * Check if user has premium access for voice features.
   */
  async checkPremiumAccess(): Promise<boolean> {
    try {
      const user = auth.currentUser;
      if (!user) return false;

      const token = await user.getIdTokenResult();
      return token.claims.premium === true;
    } catch (error) {
      console.error('Failed to check premium access:', error);
      return false;
    }
  }

  /**
   * Start recording audio for speech-to-text.
   */
  async startRecording(
    onPartialText: (text: string, confidence: number) => void,
    onFinalText: (text: string, confidence: number) => void,
    onError: (error: string) => void,
    onWaveformUpdate?: (data: number[]) => void
  ): Promise<void> {
    if (!this.isVoiceSupported()) {
      throw new Error('Voice recording not supported on this platform');
    }

    if (!(await this.checkPremiumAccess())) {
      throw new Error('Premium subscription required for voice features');
    }

    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Setup audio context for waveform visualization
      if (onWaveformUpdate) {
        this.audioContext = new AudioContext();
        this.analyser = this.audioContext.createAnalyser();
        const source = this.audioContext.createMediaStreamSource(stream);
        source.connect(this.analyser);
        this.analyser.fftSize = 256;
        this.startWaveformAnalysis(onWaveformUpdate);
      }

      // Setup MediaRecorder
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      this.recordingChunks = [];
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordingChunks.push(event.data);
          this.sendAudioChunk(event.data);
        }
      };

      // Connect to STT WebSocket
      await this.connectSTTWebSocket(onPartialText, onFinalText, onError);

      // Start recording
      this.mediaRecorder.start(1000); // Send chunks every 1 second
      this.isRecording = true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw new Error('Failed to start voice recording');
    }
  }

  /**
   * Stop recording audio.
   */
  async stopRecording(): Promise<void> {
    if (!this.isRecording) return;

    this.isRecording = false;

    // Stop MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    }

    // Close WebSocket
    if (this.sttWebSocket) {
      this.sttWebSocket.close();
      this.sttWebSocket = null;
    }

    // Clean up audio context
    if (this.audioContext) {
      await this.audioContext.close();
      this.audioContext = null;
      this.analyser = null;
    }

    this.mediaRecorder = null;
  }

  /**
   * Convert text to speech and return audio URL.
   */
  async textToSpeech(request: TTSRequest): Promise<string> {
    if (!(await this.checkPremiumAccess())) {
      throw new Error('Premium subscription required for voice features');
    }

    try {
      const user = auth.currentUser;
      if (!user) throw new Error('Authentication required');

      const token = await user.getIdToken();
      const response = await fetch(`${this.baseUrl}/v1/speech/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          text: request.text,
          voice: request.voice || 'english_female',
          language: request.language || 'en',
        }),
      });

      if (!response.ok) {
        if (response.status === 402) {
          throw new Error('Premium subscription required');
        }
        throw new Error(`TTS failed: ${response.statusText}`);
      }

      // Create blob URL for audio playback
      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);
    } catch (error) {
      console.error('TTS error:', error);
      throw error;
    }
  }

  /**
   * Play audio from URL.
   */
  async playAudio(audioUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      
      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        resolve();
      };
      
      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        reject(new Error('Failed to play audio'));
      };
      
      audio.play().catch(reject);
    });
  }

  private async connectSTTWebSocket(
    onPartialText: (text: string, confidence: number) => void,
    onFinalText: (text: string, confidence: number) => void,
    onError: (error: string) => void
  ): Promise<void> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const token = await user.getIdToken();
    const wsUrl = `${this.wsUrl}/v1/speech/stt?token=${encodeURIComponent(token)}`;

    this.sttWebSocket = new WebSocket(wsUrl);

    this.sttWebSocket.onopen = () => {
      console.log('STT WebSocket connected');
    };

    this.sttWebSocket.onmessage = (event) => {
      try {
        const response: STTResponse = JSON.parse(event.data);
        
        switch (response.type) {
          case 'partial':
            onPartialText(response.text, response.confidence);
            break;
          case 'final':
            onFinalText(response.text, response.confidence);
            break;
          case 'error':
            onError(response.text);
            break;
        }
      } catch (error) {
        console.error('Failed to parse STT response:', error);
        onError('Speech recognition error');
      }
    };

    this.sttWebSocket.onerror = (error) => {
      console.error('STT WebSocket error:', error);
      onError('Connection error');
    };

    this.sttWebSocket.onclose = () => {
      console.log('STT WebSocket disconnected');
    };

    // Wait for connection
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000);

      this.sttWebSocket!.onopen = () => {
        clearTimeout(timeout);
        resolve();
      };

      this.sttWebSocket!.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('WebSocket connection failed'));
      };
    });
  }

  private sendAudioChunk(chunk: Blob): void {
    if (this.sttWebSocket && this.sttWebSocket.readyState === WebSocket.OPEN) {
      chunk.arrayBuffer().then(buffer => {
        this.sttWebSocket!.send(buffer);
      });
    }
  }

  private startWaveformAnalysis(onUpdate: (data: number[]) => void): void {
    if (!this.analyser) return;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const analyze = () => {
      if (!this.analyser || !this.isRecording) return;

      this.analyser.getByteFrequencyData(dataArray);
      
      // Convert to normalized array for visualization
      const waveformData = Array.from(dataArray).map(value => value / 255);
      onUpdate(waveformData);

      requestAnimationFrame(analyze);
    };

    analyze();
  }
}

export const voiceService = new VoiceService();
