/**
 * Hook for managing voice recording and text-to-speech functionality.
 */

import { useState, useCallback, useRef } from 'react';
import { voiceService, VoiceRecordingState, TTSRequest } from '../services/voice';

interface UseVoiceReturn {
  // Recording state
  recordingState: VoiceRecordingState;
  isVoiceSupported: boolean;
  isPremium: boolean;
  
  // Recording controls
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  
  // Text-to-speech
  speakText: (text: string, options?: Partial<TTSRequest>) => Promise<void>;
  stopSpeaking: () => void;
  isSpeaking: boolean;
  
  // Permissions
  checkPermissions: () => Promise<boolean>;
}

export const useVoice = (): UseVoiceReturn => {
  const [recordingState, setRecordingState] = useState<VoiceRecordingState>({
    isRecording: false,
    isProcessing: false,
    partialText: '',
    finalText: '',
    error: null,
    waveformData: [],
  });

  const [isVoiceSupported, setIsVoiceSupported] = useState(false);
  const [isPremium, setIsPremium] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  const currentAudio = useRef<HTMLAudioElement | null>(null);
  const finalTextRef = useRef<string>('');

  // Check voice support and premium status on mount
  const checkPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const supported = voiceService.isVoiceSupported();
      const premium = await voiceService.checkPremiumAccess();
      
      setIsVoiceSupported(supported);
      setIsPremium(premium);
      
      return supported && premium;
    } catch (error) {
      console.error('Failed to check voice permissions:', error);
      return false;
    }
  }, []);

  const startRecording = useCallback(async (): Promise<void> => {
    try {
      setRecordingState(prev => ({
        ...prev,
        isRecording: true,
        isProcessing: true,
        partialText: '',
        finalText: '',
        error: null,
        waveformData: [],
      }));

      finalTextRef.current = '';

      await voiceService.startRecording(
        // onPartialText
        (text: string, confidence: number) => {
          setRecordingState(prev => ({
            ...prev,
            partialText: text,
            isProcessing: true,
          }));
        },
        
        // onFinalText
        (text: string, confidence: number) => {
          finalTextRef.current = text;
          setRecordingState(prev => ({
            ...prev,
            finalText: text,
            partialText: '',
            isProcessing: false,
          }));
        },
        
        // onError
        (error: string) => {
          setRecordingState(prev => ({
            ...prev,
            error,
            isRecording: false,
            isProcessing: false,
          }));
        },
        
        // onWaveformUpdate
        (waveformData: number[]) => {
          setRecordingState(prev => ({
            ...prev,
            waveformData,
          }));
        }
      );

      setRecordingState(prev => ({
        ...prev,
        isProcessing: false,
      }));
    } catch (error) {
      console.error('Failed to start recording:', error);
      setRecordingState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to start recording',
        isRecording: false,
        isProcessing: false,
      }));
    }
  }, []);

  const stopRecording = useCallback(async (): Promise<void> => {
    try {
      await voiceService.stopRecording();
      
      setRecordingState(prev => ({
        ...prev,
        isRecording: false,
        isProcessing: false,
        waveformData: [],
      }));
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setRecordingState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to stop recording',
        isRecording: false,
        isProcessing: false,
      }));
    }
  }, []);

  const speakText = useCallback(async (
    text: string,
    options: Partial<TTSRequest> = {}
  ): Promise<void> => {
    try {
      // Stop any currently playing audio
      if (currentAudio.current) {
        currentAudio.current.pause();
        currentAudio.current = null;
      }

      setIsSpeaking(true);

      const audioUrl = await voiceService.textToSpeech({
        text,
        voice: options.voice || 'english_female',
        language: options.language || 'en',
      });

      await voiceService.playAudio(audioUrl);
      
      setIsSpeaking(false);
    } catch (error) {
      console.error('Failed to speak text:', error);
      setIsSpeaking(false);
      throw error;
    }
  }, []);

  const stopSpeaking = useCallback((): void => {
    if (currentAudio.current) {
      currentAudio.current.pause();
      currentAudio.current = null;
    }
    setIsSpeaking(false);
  }, []);

  return {
    recordingState,
    isVoiceSupported,
    isPremium,
    startRecording,
    stopRecording,
    speakText,
    stopSpeaking,
    isSpeaking,
    checkPermissions,
  };
};
