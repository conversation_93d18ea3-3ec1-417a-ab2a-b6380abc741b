# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database drivers
pymongo==4.6.0
motor==3.3.2
neo4j==5.15.0

# Authentication
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
firebase-admin==6.4.0

# HTTP client for TGI
httpx==0.25.2
aiohttp==3.9.1

# LangChain for conversation management
langchain==0.1.0
langchain-community==0.0.10

# Rate limiting
slowapi==0.1.9

# Environment and configuration
python-dotenv==1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Data validation and serialization
marshmallow==3.20.1
