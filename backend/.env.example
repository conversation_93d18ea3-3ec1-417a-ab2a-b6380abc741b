# Bible Companion Backend Configuration

# Application
DEBUG=true
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8081", "http://localhost:19006"]

# Database
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=bible_companion

# Neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password

# TGI (Text Generation Inference)
TGI_BASE_URL=http://localhost:8080
TGI_TIMEOUT=30
TGI_MAX_RETRIES=3

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_PATH=path/to/firebase-service-account.json

# Chat Configuration
CHAT_MAX_HISTORY=10
CHAT_CONTEXT_VERSES=20
CHAT_MAX_TOKENS=512
CHAT_TEMPERATURE=0.7

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
