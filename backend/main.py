"""
Bible Companion Backend API

FastAPI application with Gemma chat integration, MongoDB storage,
and Firebase authentication.
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address

from backend.core.config import get_settings
from backend.core.database import mongodb_client, neo4j_driver
from backend.core.exceptions import BibleCompanionException
from backend.routers import chat, health

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    settings = get_settings()

    # Startup
    logger.info("Starting Bible Companion API", version="1.0.0")

    try:
        # Initialize MongoDB connection
        await mongodb_client.connect()
        logger.info("Connected to MongoDB")

        # Initialize Neo4j connection
        await neo4j_driver.verify_connectivity()
        logger.info("Connected to Neo4j")

        yield

    except Exception as e:
        logger.error("Failed to initialize connections", error=str(e))
        raise
    finally:
        # Shutdown
        logger.info("Shutting down Bible Companion API")

        # Close database connections
        await mongodb_client.close()
        await neo4j_driver.close()
        logger.info("Database connections closed")

def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()

    app = FastAPI(
        title="Bible Companion API",
        description="Backend API for Bible Companion app with Gemma chat integration",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )

    # Add rate limiting
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )

    # Custom exception handler
    @app.exception_handler(BibleCompanionException)
    async def bible_companion_exception_handler(
        request: Request, exc: BibleCompanionException
    ) -> JSONResponse:
        logger.error(
            "Bible Companion exception",
            error=exc.message,
            status_code=exc.status_code,
            path=request.url.path
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.message, "type": exc.__class__.__name__}
        )

    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        logger.error(
            "Unhandled exception",
            error=str(exc),
            path=request.url.path,
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={"error": "Internal server error"}
        )

    # Include routers
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(chat.router, prefix="/v1", tags=["chat"])

    return app

# Create app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn

    settings = get_settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
